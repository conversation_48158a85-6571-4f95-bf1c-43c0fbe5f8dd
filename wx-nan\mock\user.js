// 模拟用户数据
const mockUserInfo = {
  id: 1,
  name: '小明',
  avatar: '', // 头像图片地址
  phone: '13888888888',
  role: 'user', // 用户角色: admin/user
  created: '2025-04-30'
};

// 模拟家庭留言数据
const mockMessages = [
  {
    id: 1,
    content: '明天能不能加个鱼香肉丝？',
    user_name: '爸爸',
    user_id: 2,
    created_at: '2025-04-30 10:00:00',
    read: false
  },
  {
    id: 2,
    content: '明天多做点青菜！',
    user_name: '妈妈',
    user_id: 3,
    created_at: '2025-04-30 09:30:00',
    read: true
  },
  {
    id: 3,
    content: '我想吃糖醋里脊！',
    user_name: '姐姐',
    user_id: 4,
    created_at: '2025-04-30 09:00:00',
    read: true
  },
  {
    id: 4,
    content: '少放点辣椒哈～',
    user_name: '爷爷',
    user_id: 5,
    created_at: '2025-04-30 08:30:00',
    read: true
  }
];

// 模拟家庭成员数据
const mockFamilyMembers = [
  {
    id: 1,
    name: '小明',
    role: 'user',
    avatar: ''
  },
  {
    id: 2,
    name: '爸爸',
    role: 'admin',
    avatar: ''
  },
  {
    id: 3,
    name: '妈妈',
    role: 'admin',
    avatar: ''
  },
  {
    id: 4,
    name: '姐姐',
    role: 'user',
    avatar: ''
  },
  {
    id: 5,
    name: '爷爷',
    role: 'user',
    avatar: ''
  }
];

module.exports = {
  mockUserInfo,
  mockMessages,
  mockFamilyMembers
};
