.detail-container {
  background: #121212;
  color: #fff;
  min-height: 100vh;
  padding: 40rpx 20rpx;
}

.detail-card {
  background: linear-gradient(120deg, #1f1f1f 60%, #2a2a2a 100%);
  border-radius: 44rpx;
  box-shadow:
    0 12rpx 64rpx rgba(0, 0, 0, 0.6),
    0 3rpx 0 #232323 inset;
  margin: 50rpx auto 0 auto;
  padding: 44rpx 30rpx 30rpx 30rpx;
  border: 3rpx solid #232323;
  max-width: 840rpx;
  position: relative;
  overflow: hidden;
}

.detail-img {
  width: 240rpx;
  height: 240rpx;
  border-radius: 36rpx;
  object-fit: cover;
  background: #232323;
  border: 5rpx solid #00f2ea;
  box-shadow: 0 4rpx 24rpx rgba(0, 242, 234, 0.2);
  display: block;
  margin: 0 auto 24rpx auto;
}

.detail-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #00f2ea;
  text-align: center;
  margin-bottom: 10rpx;
  letter-spacing: 2rpx;
}

.detail-remark {
  color: #b3e0f7;
  font-size: 30rpx;
  text-align: center;
  margin-bottom: 24rpx;
}

.detail-section {
  margin-bottom: 22rpx;
}

.detail-label {
  color: #fe2c55;
  font-weight: bold;
  margin-bottom: 6rpx;
  display: flex;
  align-items: center;
  font-size: 32rpx;
}

.icon-margin {
  margin-right: 10rpx;
}

.detail-content {
  color: #f5f6fa;
  font-size: 30rpx;
  line-height: 1.7;
  background: #181a20;
  border-radius: 20rpx;
  padding: 14rpx 20rpx;
  margin-bottom: 4rpx;
  word-break: break-all;
}

.back-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 40rpx auto 0 auto;
  background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
  color: #fff;
  font-weight: bold;
  padding: 10rpx 44rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);
  width: 200rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    opacity: 0.9;
    box-shadow: 0 2rpx 8rpx rgba(254, 44, 85, 0.2);
  }
}
