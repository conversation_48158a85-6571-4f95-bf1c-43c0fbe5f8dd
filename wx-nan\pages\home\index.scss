.container {
  height: 100vh !important;
  background: #23272f;
  color: #fff;
  font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  letter-spacing: 0.02em;
  box-sizing: border-box;
  padding: 24rpx;
  width: 100vw !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
  /* 隐藏滚动条 */
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    background: transparent;
  }
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 主卡片
.home-welcome-card {
  border-radius: 48rpx;
  box-shadow:
    0 20rpx 20rpx #00f2ea33,
    0 0rpx 0 #232323 inset,
    0 0 0 2rpx #00f2ea inset;
  margin-bottom: 0;
  padding: 60rpx 48rpx 48rpx 48rpx;
  border: 4rpx solid rgba(200, 200, 220, 0.18);
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(0, 242, 234, 0.1) 0%,
    rgba(35, 39, 47, 0.98) 70%,
    rgba(254, 44, 85, 0.1) 100%
  );
  backdrop-filter: blur(8rpx);
  .home-welcome-left {
    flex: 1;
    min-width: 0;
  }
  .home-welcome-title {
    font-size: 40rpx;
    font-weight: 800;
    color: rgba(236, 72, 153, 1);
    margin-bottom: 12rpx;
    letter-spacing: 2rpx;
    text-align: left;
  }
  .home-welcome-desc {
    color: #e5e5e5;
    font-size: 28rpx;
    font-weight: 400;
    text-align: left;
    line-height: 1.7;
  }
  .home-welcome-img {
    width: 108rpx;
    height: 108rpx;
    border-radius: 24rpx;
    object-fit: cover;
    margin-left: 24rpx;
    box-shadow: 0 8rpx 48rpx #00f2ea33;
    background: #181a20;
    flex-shrink: 0;
  }
  &::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 14rpx;
    background: linear-gradient(90deg, #00f2ea 0%, #fe2c55 100%);
    border-radius: 48rpx 48rpx 0 0;
    opacity: 0.95;
    z-index: 2;
  }
  &::after {
    content: "";
    display: block;
    position: absolute;
    left: 36rpx;
    right: 36rpx;
    top: 26rpx;
    height: 6rpx;
    background: linear-gradient(90deg, #fff8 0%, #fff0 100%);
    border-radius: 48rpx;
    opacity: 0.18;
    z-index: 2;
  }
}

// 今日菜单卡片
.home-menu-card {
  width: calc(100% - 48rpx);
  border-radius: 32rpx;
  box-shadow:
    0 8rpx 48rpx #0007,
    0 6rpx 0 #232323 inset;
  margin-bottom: 0;
  padding: 40rpx 32rpx 32rpx 32rpx;
  border: 3rpx solid rgba(200, 200, 220, 0.18);
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #2d313a 60%, #3a3a40 100%);
  &::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 8rpx;
    background: linear-gradient(90deg, #00f2ea 0%, #ec4899 100%);
    border-radius: 32rpx 32rpx 0 0;
    opacity: 0.5;
    z-index: 2;
  }
  &::after {
    content: "";
    display: block;
    position: absolute;
    left: 20rpx;
    right: 20rpx;
    top: 14rpx;
    height: 4rpx;
    background: linear-gradient(90deg, #fff2 0%, #fff0 100%);
    border-radius: 32rpx;
    opacity: 0.1;
    z-index: 2;
  }
  .home-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32rpx;
    .home-menu-title {
      color: rgba(236, 72, 153, 1);
      font-size: 32rpx;
      font-weight: 600;
      display: flex;
      flex-direction: column;
      letter-spacing: 2rpx;

      .title-content {
        display: flex;
        align-items: center;
        gap: 8rpx;
        height: 48rpx;
        line-height: 48rpx;
      }

      .menu-date {
        font-size: 24rpx;
        color: #00f2ea;
        font-weight: normal;
        margin-top: 4rpx;

        &.recommended {
          color: #fe2c55;
          font-style: italic;
        }
      }
    }
    .theme-link {
      color: #fff !important;
      text-decoration: none !important;
      font-weight: 500;
      font-size: 26rpx;
      border: none !important;
      background: none !important;
      padding: 0 12rpx;
      width: 200rpx !important;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      box-shadow: none !important;
      line-height: 48rpx;
      height: 48rpx;
      transition: all 0.3s ease;

      .van-icon {
        margin-left: 4rpx;
        transition: transform 0.3s ease;
      }

      &:active,
      &.theme-link-hover {
        color: rgba(236, 72, 153, 1) !important;

        .van-icon {
          transform: translateX(4rpx);
        }
      }
    }
  }
  .home-menu-list {
    display: flex;
    flex-direction: row;
    gap: 24rpx;
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 12rpx;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      background: transparent;
    }
    .home-menu-food-card {
      min-width: 200rpx;
      max-width: 220rpx;
      border-radius: 36rpx;
      box-shadow: 0 8rpx 32rpx #00f2ea22;
      background: linear-gradient(135deg, #3a3a40 60%, #4a4a55 100%);
      padding: 32rpx 16rpx 20rpx 16rpx;
      height: 200rpx;
      margin-top: 24rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      border: 2rpx solid rgba(200, 200, 220, 0.18);
      transition: all 0.3s ease;
      justify-content: flex-start;
      animation: fadeInUp 0.5s ease-out;

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20rpx);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      &:active {
        box-shadow: 0 12rpx 40rpx #00f2ea44;
        transform: translateY(-4rpx) scale(1.02);
      }
      .home-menu-food-img {
        width: 88rpx;
        height: 88rpx;
        border-radius: 50%;
        object-fit: cover;
        border: 4rpx solid #00f2ea;
        margin-bottom: 18rpx;
        background: #23272f;
        box-shadow: 0 2rpx 8rpx #0004;
      }
      .home-menu-food-name {
        color: #fff;
        font-weight: 600;
        font-size: 30rpx;
        margin-bottom: 6rpx;
        letter-spacing: 1rpx;
        text-align: center;
        line-height: 36rpx;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .home-menu-food-count {
        background: #232323;
        color: rgba(236, 72, 153, 1);
        font-size: 20rpx;
        font-weight: bold;
        border-radius: 999rpx;
        padding: 0 16rpx;
        position: absolute;
        top: 14rpx;
        right: 14rpx;
        box-shadow: 0 2rpx 8rpx #0004;
        line-height: 32rpx;
        height: 32rpx;
        min-width: 44rpx;
        text-align: center;
        z-index: 3;
        border: 1px solid rgba(236, 72, 153, 0.3);
      }
    }
  }
}

// 家庭留言卡片
.home-message-card {
  width: calc(100% - 48rpx);
  border-radius: 32rpx;
  box-shadow:
    0 8rpx 48rpx #0007,
    0 6rpx 0 #232323 inset;
  margin-bottom: 0;
  padding: 40rpx 24rpx 24rpx 24rpx;
  border: 1rpx solid rgba(200, 200, 220, 0.18);
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(35, 39, 47, 0.92) 60%, rgba(24, 26, 32, 0.88) 100%);
  &::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #00f2ea 0%, #fe2c55 100%);
    border-radius: 32rpx 32rpx 0 0;
    opacity: 0.5;
    z-index: 2;
  }
  &::after {
    content: "";
    display: block;
    position: absolute;
    left: 20rpx;
    right: 20rpx;
    top: 14rpx;
    height: 4rpx;
    background: linear-gradient(90deg, #fff2 0%, #fff0 100%);
    border-radius: 32rpx;
    opacity: 0.1;
    z-index: 2;
  }
  .home-message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .theme-link {
      color: #fff !important;
      text-decoration: none !important;
      font-weight: 500;
      font-size: 24rpx;
      border: none !important;
      background: none !important;
      padding: 0 12rpx;
      min-width: 80rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      box-shadow: none !important;
      line-height: 48rpx;
      height: 48rpx;
      transition: all 0.3s ease;

      .van-icon {
        margin-left: 4rpx;
        transition: transform 0.3s ease;
        font-size: 24rpx !important;
      }

      &:active,
      &.theme-link-hover {
        color: rgba(236, 72, 153, 1) !important;

        .van-icon {
          transform: translateX(4rpx);
        }
      }
    }
  }

  .home-message-title {
    color: rgba(236, 72, 153, 1);
    font-size: 32rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    letter-spacing: 2rpx;
    gap: 8rpx;
  }
  .home-message-swiper {
    height: 96rpx;
    margin-top: 8rpx;
    .home-message-swipe {
      height: 96rpx;
      width: 100%;
    }
    swiper-item {
      height: 96rpx;
      line-height: 96rpx;
      overflow: hidden;
    }
    .home-message-text {
      color: #e5e5e5;
      font-size: 24rpx;
      text-align: center;
      line-height: 96rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.notice-bar-wrap {
  width: 100%;
  margin-bottom: 12rpx;
  box-sizing: border-box;
  width: calc(100% - 48rpx);
  height: 78rpx;
}
.van-notice-bar {
  background: linear-gradient(90deg, #232323 60%, #181a20 100%) !important;
  border-radius: 32rpx !important;
  padding: 24rpx 24rpx !important;
  color: #00f2ea !important;
  font-size: 28rpx !important;
  font-weight: 600;
  box-shadow: 0 8rpx 36rpx #00f2ea22;
  border: 1rpx solid #00f2ea;
  display: flex;
  align-items: center;
  height: 78rpx !important;
  margin: 0 !important;
  .van-icon {
    color: rgba(236, 72, 153, 1) !important;
    font-size: 36rpx !important;
    margin-right: 16rpx;
  }
}
