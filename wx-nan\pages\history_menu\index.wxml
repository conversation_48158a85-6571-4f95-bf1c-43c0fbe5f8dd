<view class="container">
  <view class="page-header">
    <view class="page-title">
      {{viewingToday ? '今日菜单' : '历史菜单'}}
    </view>
  </view>

  <view class="history-section">
    <view class="history-list">
      <view
        wx:for="{{historyMenus}}"
        wx:key="id"
        class="history-card {{item.isToday ? 'today-card' : ''}}"
      >
        <view class="history-date">
          <van-icon name="calendar-o" class="icon-margin" />{{item.date}}
          <view wx:if="{{item.isToday}}" class="today-tag">今日</view>
        </view>
        <view class="history-summary">{{item.summary}}</view>
        <view class="history-remark">
          <van-icon
            name="notes-o"
            class="icon-margin"
          />备注：{{item.remark || '无'}}
        </view>
      </view>
    </view>

    <view class="back-btn" bindtap="goBack">
      <van-icon name="arrow-left" class="icon-margin" />返回
    </view>
  </view>
</view>
