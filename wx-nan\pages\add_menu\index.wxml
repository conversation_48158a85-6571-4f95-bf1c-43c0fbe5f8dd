<view class="container">
  <view class="main-card">
    <view class="section-title">
      <van-icon name="plus" class="icon-margin" />新增菜单
    </view>

    <view class="input-container">
      <input
        class="input-warm"
        placeholder="菜品名称，如红烧肉"
        placeholder-class="placeholder-style"
        bindinput="onNameInput"
        value="{{name}}"
      />
    </view>

    <view class="upload-img-box">
      <view class="upload-img-label" bindtap="chooseImage">
        <van-icon name="photo-o" class="icon-margin" />上传图片
      </view>
      <image
        wx:if="{{tempImagePath}}"
        class="upload-img-preview"
        src="{{tempImagePath}}"
        mode="aspectFill"
      />
    </view>

    <view class="input-container">
      <textarea
        class="input-warm textarea-style"
        placeholder="备注（如口味、适合人群等，可选）"
        placeholder-class="placeholder-style"
        bindinput="onRemarkInput"
        value="{{remark}}"
        auto-height
        maxlength="200"
        show-confirm-bar="{{false}}"
      ></textarea>
    </view>

    <view class="input-container">
      <textarea
        class="input-warm textarea-style"
        placeholder="原材料（如五花肉、酱油、糖等）"
        placeholder-class="placeholder-style"
        bindinput="onMaterialInput"
        value="{{material}}"
        auto-height
        maxlength="200"
        show-confirm-bar="{{false}}"
      ></textarea>
    </view>

    <view class="input-container">
      <textarea
        class="input-warm textarea-style"
        placeholder="做法（如步骤说明）"
        placeholder-class="placeholder-style"
        bindinput="onMethodInput"
        value="{{method}}"
        auto-height
        maxlength="500"
        show-confirm-bar="{{false}}"
      ></textarea>
    </view>

    <view class="submit-btn" bindtap="submitForm">新增</view>
  </view>
</view>
