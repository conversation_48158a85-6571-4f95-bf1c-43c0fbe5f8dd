<view class="container">
  <view class="login-card">
    <!-- 登录方式切换 -->
    <view class="login-tabs">
      <view
        class="login-tab {{loginType === 'password' ? 'active' : ''}}"
        bindtap="switchLoginType"
        data-type="password"
      >
        <van-icon name="user-circle-o" class="tab-icon" />
        <text>账号密码登录</text>
      </view>
      <view
        class="login-tab {{loginType === 'wechat' ? 'active' : ''}}"
        bindtap="switchLoginType"
        data-type="wechat"
      >
        <van-icon name="wechat" class="tab-icon" />
        <text>微信一键登录</text>
      </view>
    </view>

    <!-- 账号密码登录 -->
    <view class="login-form" wx:if="{{loginType === 'password'}}">
      <view class="input-container">
        <van-icon name="phone-o" class="input-icon" />
        <input
          class="login-input"
          id="login-username"
          placeholder="手机号/账号"
          placeholder-class="placeholder-style"
          bindinput="onUsernameInput"
          maxlength="11"
        />
      </view>
      <view class="input-container">
        <van-icon name="lock" class="input-icon" />
        <input
          class="login-input"
          id="login-password"
          password="{{true}}"
          placeholder="密码"
          placeholder-class="placeholder-style"
          bindinput="onPasswordInput"
        />
      </view>
      <view class="login-options">
        <view class="remember-pwd">
          <checkbox
            checked="{{rememberPwd}}"
            bindtap="toggleRememberPwd"
            color="#00f2ea"
          />
          <text bindtap="toggleRememberPwd">记住密码</text>
        </view>
        <view class="forget-pwd" bindtap="forgetPassword">忘记密码?</view>
      </view>
      <view class="login-tip">{{loginTip}}</view>
      <view
        class="login-btn {{loading ? 'loading' : ''}}"
        bindtap="loginWithPassword"
      >
        <text wx:if="{{!loading}}">登录</text>
        <view wx:else class="loading-icon"></view>
      </view>
    </view>

    <!-- 微信一键登录 -->
    <view class="login-form wechat-login" wx:if="{{loginType === 'wechat'}}">
      <view class="wechat-avatar">
        <van-icon name="wechat" size="80rpx" color="#00f2ea" />
      </view>
      <view class="wechat-tip">点击下方按钮，快速登录</view>
      <view class="login-tip">{{loginTip}}</view>
      <button
        class="wechat-login-btn {{loading ? 'loading' : ''}}"
        open-type="getUserInfo"
        bindgetuserinfo="loginWithWechat"
      >
        <text wx:if="{{!loading}}">微信一键登录</text>
        <view wx:else class="loading-icon"></view>
      </button>
      <view class="wechat-privacy">
        登录即表示您已同意<text class="privacy-link" bindtap="showPrivacy"
          >《用户协议和隐私政策》</text
        >
      </view>
    </view>
  </view>
</view>
