.container {
  background: #121212;
  color: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 100rpx;
}

.login-card {
  width: 680rpx;
  background: linear-gradient(120deg, #1f1f1f 60%, #2a2a2a 100%);
  border-radius: 44rpx;
  box-shadow:
    0 12rpx 64rpx rgba(0, 0, 0, 0.6),
    0 3rpx 0 #232323 inset;
  padding: 30rpx 30rpx 40rpx 30rpx;
  border: 3rpx solid #232323;
  position: relative;
  overflow: hidden;
}

/* 登录方式切换 */
.login-tabs {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  border-bottom: 2rpx solid #333;
  padding-bottom: 20rpx;
}

.login-tab {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #999;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  transition: all 0.3s;

  &.active {
    color: #00f2ea;

    &:after {
      content: "";
      position: absolute;
      bottom: -20rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background: #00f2ea;
      border-radius: 4rpx;
    }
  }
}

.tab-icon {
  font-size: 48rpx !important;
  margin-bottom: 8rpx;
}

/* 登录表单 */
.login-form {
  padding: 20rpx 0;
}

.input-container {
  width: 100%;
  margin-bottom: 24rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 24rpx;
  font-size: 36rpx !important;
  color: #00f2ea;
}

.login-input {
  width: 100%;
  background: #232323;
  color: #fff;
  border: 2rpx solid #00f2ea;
  border-radius: 20rpx;
  padding: 0 24rpx 0 80rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  height: 88rpx;
  line-height: 88rpx;
}

.placeholder-style {
  color: rgba(255, 255, 255, 0.5);
  font-size: 28rpx;
  line-height: 88rpx;
}

.login-btn {
  width: 100%;
  background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
  color: #fff;
  font-weight: bold;
  padding: 14rpx 0;
  border-radius: 24rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);
  margin-bottom: 24rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;

  &.loading {
    opacity: 0.8;
  }
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.remember-pwd {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #e6e6e6;

  text {
    margin-left: 8rpx;
  }
}

.forget-pwd {
  font-size: 26rpx;
  color: #00f2ea;
}

.login-tip {
  color: #fe2c55;
  text-align: center;
  font-size: 28rpx;
  min-height: 40rpx;
  margin-bottom: 20rpx;
}

/* 微信登录样式 */
.wechat-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}

.wechat-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: rgba(0, 242, 234, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  border: 4rpx solid rgba(0, 242, 234, 0.3);
}

.wechat-tip {
  font-size: 32rpx;
  color: #fff;
  margin-bottom: 40rpx;
}

.wechat-login-btn {
  width: 100%;
  background: #07c160;
  color: #fff;
  font-weight: bold;
  padding: 0;
  border-radius: 24rpx;
  font-size: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);
  margin-bottom: 24rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  border: none;

  &.loading {
    opacity: 0.8;
  }

  &::after {
    border: none;
  }
}

.wechat-privacy {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 20rpx;
}

.privacy-link {
  color: #00f2ea;
}
