<view class="detail-container">
  <view class="detail-card">
    <image class="detail-img" src="{{menuItem.img}}" mode="aspectFill" />
    <view class="detail-title">{{menuItem.name}}</view>
    <view class="detail-remark">{{menuItem.remark}}</view>

    <view class="detail-section">
      <text class="detail-label">
        <van-icon name="flower-o" class="icon-margin" />原材料
      </text>
      <view class="detail-content">{{menuItem.material}}</view>
    </view>

    <view class="detail-section">
      <text class="detail-label">
        <van-icon name="orders-o" class="icon-margin" />做法
      </text>
      <view class="detail-content">{{menuItem.method}}</view>
    </view>

    <view class="back-btn" bindtap="onBack">
      <van-icon name="arrow-left" class="icon-margin" />返回
    </view>
  </view>
</view>
