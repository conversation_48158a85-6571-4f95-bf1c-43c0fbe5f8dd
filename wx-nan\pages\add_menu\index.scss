.container {
  background: #121212;
  min-height: 100vh;
  padding: 40rpx 20rpx;
}

.main-card {
  background: linear-gradient(120deg, #1f1f1f 60%, #2a2a2a 100%);
  border-radius: 44rpx;
  box-shadow:
    0 12rpx 64rpx rgba(0, 0, 0, 0.6),
    0 3rpx 0 #232323 inset;
  margin-bottom: 56rpx;
  padding: 56rpx 40rpx;
  border: 3rpx solid #232323;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    height: 8rpx;
    background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
    border-radius: 44rpx 44rpx 0 0;
    opacity: 0.8;
  }
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fe2c55;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  letter-spacing: 2rpx;
}

.icon-margin {
  margin-right: 16rpx;
}

.input-container {
  margin-bottom: 36rpx;
  width: 100%;
  position: relative;
}

.input-warm {
  background: #232323;
  color: #fff;
  border: 2rpx solid #fe2c55;
  border-radius: 24rpx;
  padding: 0 32rpx;
  font-size: 30rpx;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
  height: 88rpx;
  line-height: 88rpx;
}

.textarea-style {
  min-height: 100rpx;
  line-height: 1.4;
  padding: 24rpx 32rpx;
}

.placeholder-style {
  color: rgba(255, 255, 255, 0.5);
  font-size: 28rpx;
  line-height: 88rpx;
}

.upload-img-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 36rpx;
}

.upload-img-preview {
  width: 144rpx;
  height: 144rpx;
  border-radius: 24rpx;
  object-fit: cover;
  background: #232323;
  border: 3rpx solid #00f2ea;
  box-shadow: 0 4rpx 16rpx rgba(0, 242, 234, 0.2);
}

.upload-img-label {
  display: inline-block;
  background: linear-gradient(90deg, #fe2c55 0%, #00f2ea 100%);
  color: #fff;
  font-weight: bold;
  border-radius: 16rpx;
  padding: 20rpx 48rpx;
  font-size: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);
}

.submit-btn {
  background: linear-gradient(90deg, #fe2c55 60%, #00f2ea 100%);
  color: #fff;
  border: none;
  border-radius: 28rpx;
  padding: 24rpx 0;
  font-size: 32rpx;
  font-weight: 600;
  width: 100%;
  margin-top: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(254, 44, 85, 0.2);
  text-align: center;
}
