Page({
  data: {
    name: '',
    tempImagePath: '',
    remark: '',
    material: '',
    method: ''
  },

  onLoad() {
    // 页面加载时的逻辑
  },

  // 输入菜品名称
  onNameInput(e) {
    this.setData({
      name: e.detail.value
    });
  },

  // 输入备注
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  // 输入原材料
  onMaterialInput(e) {
    this.setData({
      material: e.detail.value
    });
  },

  // 输入做法
  onMethodInput(e) {
    this.setData({
      method: e.detail.value
    });
  },

  // 选择图片
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: res => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.setData({
          tempImagePath: tempFilePath
        });
      }
    });
  },

  // 提交表单
  submitForm() {
    const {name, tempImagePath, remark, material, method} = this.data;

    // 验证必填字段
    if (!name) {
      wx.showToast({
        title: '请输入菜品名称',
        icon: 'none'
      });
      return;
    }

    // 这里可以添加上传图片到服务器的逻辑
    // 然后将菜品信息提交到服务器

    // 模拟提交成功
    wx.showLoading({
      title: '提交中...'
    });

    setTimeout(() => {
      wx.hideLoading();

      wx.showToast({
        title: '添加成功',
        icon: 'success',
        duration: 2000,
        success: () => {
          // 清空表单
          this.setData({
            name: '',
            tempImagePath: '',
            remark: '',
            material: '',
            method: ''
          });

          // 可以选择跳转到其他页面
          // wx.switchTab({
          //   url: '/pages/order/index'
          // });
        }
      });
    }, 1500);
  }
});
